'use client'

import { useEffect, useState } from 'react'
import { TableOfContentsItem } from '@/types'

interface TableOfContentsProps {
  content: string
}

interface TOCSection {
  h2: TableOfContentsItem
  children: TableOfContentsItem[]
}

export default function TableOfContents({ content }: TableOfContentsProps) {
  const [toc, setToc] = useState<TableOfContentsItem[]>([])
  const [activeId, setActiveId] = useState<string>('')
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set())

  useEffect(() => {
    // Extract headings from content, starting from H2 only
    const headingRegex = /<h([2-4])[^>]*id="([^"]*)"[^>]*>([^<]*)<\/h[2-4]>/g
    const headings: TableOfContentsItem[] = []
    let match

    while ((match = headingRegex.exec(content)) !== null) {
      const level = parseInt(match[1])
      const id = match[2]
      const title = match[3].trim()

      // Only include H2, H3, and H4 (starting from H2)
      // Skip H1 as it's typically the main title
      if (title && level >= 2 && level <= 4) {
        headings.push({ id, title, level })
      }
    }

    setToc(headings)
  }, [content])

  useEffect(() => {
    // Set up intersection observer for active heading
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const newActiveId = entry.target.id
            setActiveId(newActiveId)

            // Auto-expand current section and collapse others
            const activeHeading = toc.find(h => h.id === newActiveId)
            if (activeHeading) {
              let targetH2Id: string

              if (activeHeading.level === 2) {
                // If it's an H2, use it directly
                targetH2Id = newActiveId
              } else {
                // If it's H3 or H4, find its parent H2
                const parentH2 = findParentH2(toc, newActiveId)
                targetH2Id = parentH2?.id || ''
              }

              if (targetH2Id) {
                // Set only the current H2 as expanded (auto-collapse others)
                setExpandedSections(new Set([targetH2Id]))
              }
            }
          }
        })
      },
      {
        rootMargin: '-100px 0px -80% 0px',
        threshold: 0.1,
      }
    )

    // Observe all headings
    toc.forEach(({ id }) => {
      const element = document.getElementById(id)
      if (element) {
        observer.observe(element)
      }
    })

    return () => observer.disconnect()
  }, [toc])

  // Helper function to find parent H2 for H3/H4 headings
  const findParentH2 = (headings: TableOfContentsItem[], targetId: string): TableOfContentsItem | null => {
    const targetIndex = headings.findIndex(h => h.id === targetId)
    if (targetIndex === -1) return null

    // Look backwards for the nearest H2
    for (let i = targetIndex - 1; i >= 0; i--) {
      if (headings[i].level === 2) {
        return headings[i]
      }
    }
    return null
  }

  // Group headings into sections
  const groupedToc = (): TOCSection[] => {
    const sections: TOCSection[] = []
    let currentSection: TOCSection | null = null

    toc.forEach(heading => {
      if (heading.level === 2) {
        if (currentSection) {
          sections.push(currentSection)
        }
        currentSection = { h2: heading, children: [] }
      } else if (currentSection && (heading.level === 3 || heading.level === 4)) {
        currentSection.children.push(heading)
      }
    })

    if (currentSection) {
      sections.push(currentSection)
    }

    return sections
  }

  if (toc.length === 0) {
    return null
  }

  const scrollToHeading = (id: string) => {
    const element = document.getElementById(id)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set()
      // If the section is currently expanded, collapse it (empty set)
      // If it's not expanded, expand only this section
      if (!prev.has(sectionId)) {
        newSet.add(sectionId)
      }
      return newSet
    })
  }

  const sections = groupedToc()

  return (
    <nav className="sticky top-24 hidden lg:block">
      <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-6 uppercase tracking-wide">
        Table of Contents
      </h3>
      <ul className="space-y-2">
        {sections.map((section) => {
          const isExpanded = expandedSections.has(section.h2.id)
          const hasChildren = section.children.length > 0
          const isActive = activeId === section.h2.id
          const hasActiveChild = section.children.some(child => activeId === child.id)

          return (
            <li key={section.h2.id}>
              {/* H2 Heading */}
              <div className="flex items-center">
                <button
                  onClick={() => scrollToHeading(section.h2.id)}
                  className={`
                    flex-1 text-left font-bold text-sm transition-colors duration-200 hover:text-primary-500 dark:hover:text-primary-400 py-1
                    ${isActive || hasActiveChild ? 'text-primary-600 dark:text-primary-400' : 'text-gray-700 dark:text-gray-300'}
                  `}
                >
                  {section.h2.title}
                </button>
                {hasChildren && (
                  <button
                    onClick={() => toggleSection(section.h2.id)}
                    className="ml-2 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors duration-200"
                  >
                    <svg
                      className={`w-3 h-3 text-gray-400 dark:text-gray-500 transition-transform duration-200 ${
                        isExpanded ? 'rotate-90' : ''
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                )}
              </div>

              {/* H3 and H4 Children */}
              {hasChildren && (
                <div
                  className={`
                    overflow-hidden transition-all duration-300 ease-in-out
                    ${isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}
                  `}
                >
                  <ul className="mt-2 space-y-1 border-l-2 border-gray-100 dark:border-gray-700 ml-2 pl-3">
                    {section.children.map((child) => (
                      <li key={child.id}>
                        <button
                          onClick={() => scrollToHeading(child.id)}
                          className={`
                            block w-full text-left text-xs transition-colors duration-200 hover:text-primary-500 dark:hover:text-primary-400 py-1
                            ${child.level === 3 ? 'font-medium' : 'font-normal'}
                            ${child.level === 4 ? 'ml-3' : ''}
                            ${activeId === child.id ? 'text-primary-600 dark:text-primary-400' : 'text-gray-600 dark:text-gray-400'}
                          `}
                        >
                          {child.title}
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </li>
          )
        })}
      </ul>
    </nav>
  )
}
