import { getBlogPosts } from '@/lib/markdown'
import BlogGridWithFilter from '@/components/BlogGridWithFilter'

export default async function Home() {
  const posts = await getBlogPosts()

  return (
    <div className="relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center py-20 mb-16">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-blue-600 via-primary-600 to-orange-500 bg-clip-text text-transparent drop-shadow-sm">
              AI Automation, Website and App Development Expert Solutions
            </span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Thoughts on web development, AI automation, and the intersection of technology and creativity.
          </p>
        </div>

        {/* Blog Grid with Filter */}
        <div className="py-12 mb-20">
          <BlogGridWithFilter posts={posts} />
        </div>
      </div>
    </div>
  )
}
