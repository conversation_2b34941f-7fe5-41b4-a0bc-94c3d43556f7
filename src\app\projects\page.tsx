import { getProjects } from '@/lib/markdown'
import ProjectGrid from '@/components/ProjectGrid'

export const metadata = {
  title: 'Projects | <PERSON>',
  description: 'Portfolio of web development and AI automation projects by <PERSON>',
}

export default async function ProjectsPage() {
  const projects = await getProjects()
  console.log('Projects loaded:', projects.length)

  return (
    <div className="relative">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="mb-20 py-16">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span className="bg-gradient-to-r from-blue-600 dark:from-blue-400 via-primary-600 dark:via-primary-400 to-orange-500 dark:to-orange-400 bg-clip-text text-transparent">
              Turn your ideas into impactful solutions like them!
            </span>
          </h1>
        </div>

        {/* Projects Grid */}
        <div className="mb-20">
          <ProjectGrid projects={projects} />
        </div>
      </div>
    </div>
  )
}
