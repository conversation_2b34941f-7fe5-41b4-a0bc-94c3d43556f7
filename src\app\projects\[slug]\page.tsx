import { notFound } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { getProject, getProjects } from '@/lib/markdown'
import ProjectInquiryForm from '@/components/ProjectInquiryForm'

interface ProjectPageProps {
  params: {
    slug: string
  }
}

export async function generateStaticParams() {
  const projects = await getProjects()
  return projects.map((project) => ({
    slug: project.slug,
  }))
}

export async function generateMetadata({ params }: ProjectPageProps) {
  const project = await getProject(params.slug)
  
  if (!project) {
    return {
      title: 'Project Not Found',
    }
  }

  return {
    title: `${project.title} | <PERSON>`,
    description: project.description,
    openGraph: {
      title: project.title,
      description: project.description,
      images: [project.featuredImage],
    },
  }
}

export default async function ProjectPage({ params }: ProjectPageProps) {
  const project = await getProject(params.slug)

  if (!project) {
    notFound()
  }

  // Extract Challenge and Solution sections from content
  const extractSection = (content: string, sectionTitle: string): { content: string; remaining: string } => {
    const regex = new RegExp(`<h2[^>]*>${sectionTitle}</h2>([\\s\\S]*?)(?=<h2|$)`, 'i')
    const match = content.match(regex)
    if (match) {
      const sectionContent = match[1].trim()
      const remaining = content.replace(match[0], '').trim()
      return { content: sectionContent, remaining }
    }
    return { content: '', remaining: content }
  }

  const challengeExtraction = extractSection(project.content, 'The Challenge')
  const solutionExtraction = extractSection(challengeExtraction.remaining, 'The Solution')

  const challengeContent = challengeExtraction.content
  const solutionContent = solutionExtraction.content
  const mainContent = solutionExtraction.remaining

  return (
    <div className="bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Back to Projects */}
        <Link
          href="/projects"
          className="no-link-style inline-flex items-center text-gray-600 hover:text-gray-900 mb-12 transition-colors duration-300"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back to Projects
        </Link>

        {/* Services */}
        {project.technologies && project.technologies.length > 0 && (
          <div className="text-sm text-gray-600 mb-4">
            {project.technologies.map((tech, index) => (
              <span key={tech}>
                {tech}
                {index < project.technologies.length - 1 && ' / '}
              </span>
            ))}
          </div>
        )}

        {/* Project Title */}
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-gray-100 mb-12 leading-tight">
          {project.title}
        </h1>

        {/* Featured Image - Square crop on mobile, full size on desktop */}
        <div className="mb-16">
          <Image
            src={project.featuredImage}
            alt={project.title}
            width={1200}
            height={630}
            className="w-full h-80 sm:h-96 md:h-auto object-cover object-center"
            priority
          />
        </div>

        {/* Mobile Metadata - Show at top on mobile only */}
        <div className="md:hidden mb-12">
          <div className="grid grid-cols-2 gap-6 mb-8">
            {/* Left Column */}
            <div className="space-y-6">
              {/* Project Date */}
              <div>
                <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">Project Date</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {new Date(project.date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long'
                  })}
                </p>
              </div>

              {/* Industry */}
              {project.industry && (
                <div>
                  <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">Industry</h3>
                  <p className="text-gray-600 dark:text-gray-300">{project.industry}</p>
                </div>
              )}

              {/* Technology Used */}
              {project.technologies && project.technologies.length > 0 && (
                <div>
                  <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">Technology Used</h3>
                  <div className="space-y-1">
                    {project.technologies.map((tech) => (
                      <p key={tech} className="text-gray-600 dark:text-gray-300 text-sm">{tech}</p>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              {/* Client */}
              {project.client && (
                <div>
                  <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">Client</h3>
                  <p className="text-gray-600 dark:text-gray-300">{project.client}</p>
                </div>
              )}

              {/* The Challenge */}
              {challengeContent && (
                <div>
                  <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">The Challenge</h3>
                  <div
                    className="text-gray-600 dark:text-gray-300 text-sm prose prose-sm max-w-none"
                    dangerouslySetInnerHTML={{ __html: challengeContent }}
                  />
                </div>
              )}

              {/* The Solution */}
              {solutionContent && (
                <div>
                  <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">The Solution</h3>
                  <div
                    className="text-gray-600 dark:text-gray-300 text-sm prose prose-sm max-w-none"
                    dangerouslySetInnerHTML={{ __html: solutionContent }}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons on mobile */}
          <div className="space-y-3 mb-8">
            {project.liveUrl && (
              <a
                href={project.liveUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="no-link-style group flex items-center justify-center w-full px-4 py-3 bg-primary-600 dark:bg-primary-700 text-white text-sm font-medium hover:bg-primary-700 dark:hover:bg-primary-600 transition-colors duration-300 overflow-hidden"
              >
                Visit Live Site
                <span className="ml-2 group-hover:animate-marquee-ne inline-block transition-transform duration-300">↗</span>
              </a>
            )}
            {project.githubUrl && (
              <a
                href={project.githubUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="no-link-style flex items-center justify-center w-full px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-300"
              >
                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                </svg>
                View Code
              </a>
            )}
          </div>
        </div>

        {/* Content and Sidebar Layout */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-8 lg:gap-12">
          {/* Main Content */}
          <div className="md:col-span-2 lg:col-span-3">
            <div
              className="prose prose-lg max-w-none project-content"
              dangerouslySetInnerHTML={{ __html: mainContent }}
            />
          </div>

          {/* Sidebar - Metadata (Hidden on mobile, shown on desktop) */}
          <div className="hidden md:block md:col-span-1 lg:col-span-1">
            <div className="lg:sticky lg:top-24">
              {/* Mobile: 2-column grid for metadata, Desktop: single column */}
              <div className="grid grid-cols-2 lg:grid-cols-1 gap-6 lg:gap-8 mb-8">
                {/* Project Date */}
                <div>
                  <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">Project Date</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {new Date(project.date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long'
                    })}
                  </p>
                </div>

                {/* Client */}
                {project.client && (
                  <div>
                    <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">Client</h3>
                    <p className="text-gray-600 dark:text-gray-300">{project.client}</p>
                  </div>
                )}

                {/* Industry */}
                {project.industry && (
                  <div>
                    <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">Industry</h3>
                    <p className="text-gray-600 dark:text-gray-300">{project.industry}</p>
                  </div>
                )}

                {/* Technology Used */}
                {project.technologies && project.technologies.length > 0 && (
                  <div className="col-span-2 lg:col-span-1">
                    <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">Technology Used</h3>
                    <div className="space-y-1">
                      {project.technologies.map((tech) => (
                        <p key={tech} className="text-gray-600 dark:text-gray-300 text-sm">{tech}</p>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Challenge and Solution - Full width on mobile and desktop */}
              <div className="space-y-6 mb-8">
                {/* The Challenge */}
                {challengeContent && (
                  <div>
                    <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">The Challenge</h3>
                    <div
                      className="text-gray-600 dark:text-gray-300 text-sm prose prose-sm max-w-none"
                      dangerouslySetInnerHTML={{ __html: challengeContent }}
                    />
                  </div>
                )}

                {/* The Solution */}
                {solutionContent && (
                  <div>
                    <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">The Solution</h3>
                    <div
                      className="text-gray-600 dark:text-gray-300 text-sm prose prose-sm max-w-none"
                      dangerouslySetInnerHTML={{ __html: solutionContent }}
                    />
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                {project.liveUrl && (
                  <a
                    href={project.liveUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="no-link-style group flex items-center justify-center w-full px-4 py-3 bg-primary-600 dark:bg-primary-700 text-white text-sm font-medium hover:bg-primary-700 dark:hover:bg-primary-600 transition-colors duration-300 overflow-hidden"
                  >
                    Visit Live Site
                    <span className="ml-2 group-hover:animate-marquee-ne inline-block transition-transform duration-300">↗</span>
                  </a>
                )}
                {project.githubUrl && (
                  <a
                    href={project.githubUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="no-link-style flex items-center justify-center w-full px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-300"
                  >
                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                    </svg>
                    View Code
                  </a>
                )}
              </div>

              {/* Share this article */}
              <div className="mt-8">
                <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-4">Share this article</h3>
                <div className="flex space-x-4">
                  {/* Facebook */}
                  <a
                    href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(typeof window !== 'undefined' ? window.location.href : '')}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="no-link-style text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-colors duration-300"
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                  </a>

                  {/* Twitter/X */}
                  <a
                    href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(typeof window !== 'undefined' ? window.location.href : '')}&text=${encodeURIComponent(project.title)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="no-link-style text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-colors duration-300"
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                    </svg>
                  </a>

                  {/* LinkedIn */}
                  <a
                    href={`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(typeof window !== 'undefined' ? window.location.href : '')}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="no-link-style text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-colors duration-300"
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Project Inquiry Form */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-16 mt-16">
          <ProjectInquiryForm />
        </div>

        {/* Add bottom margin for footer */}
        <div className="mb-20"></div>
      </div>
    </div>
  )
}
